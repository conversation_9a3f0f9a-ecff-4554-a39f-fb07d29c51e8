defmodule QuoteX.Repo.Migrations.MigrateResources5 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:properties) do
      add :processed, :boolean, null: false, default: false
    end
  end

  def down do
    alter table(:properties) do
      remove :processed
    end
  end
end
