defmodule QuoteX.Repo.Migrations.MigrateResources6 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:operators) do
      add :encrypted_name, :binary, null: false
    end
  end

  def down do
    alter table(:operators) do
      remove :encrypted_name
    end
  end
end
