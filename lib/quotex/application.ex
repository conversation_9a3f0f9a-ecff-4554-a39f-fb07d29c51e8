defmodule QuoteX.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      QuoteXWeb.Telemetry,
      QuoteX.Repo,
      {DNSCluster, query: Application.get_env(:quotex, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: QuoteX.PubSub},
      # Start a worker by calling: QuoteX.Worker.start_link(arg)
      # {QuoteX.Worker, arg},
      # Start to serve requests, typically the last entry
      QuoteXWeb.Endpoint,
      {Oban, Application.fetch_env!(:quotex, Oban)},
      {AshAuthentication.Supervisor, [otp_app: :quotex]}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: QuoteX.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    QuoteXWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
